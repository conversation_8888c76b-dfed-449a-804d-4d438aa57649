//
//  PersonalitySignParseTest.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON><PERSON> ye on 2025/7/29.
//

import Foundation

/// 测试个性签名@用户名解析功能
class PersonalitySignParseTest {
    
    /// 测试解析功能
    static func testParseSignature() {
        print("=== 开始测试个性签名@用户名解析功能 ===")
        
        // 模拟API返回的数据
        let testData = PersonalHomeInfoData()
        testData.personalitySign = "叫爸爸@ff808081981637aa0198177d7c4d0004\\"
        testData.mentionedUser = [
            "ff808081981637aa0198177d7c4d0004": "嘿嘿一-^#"
        ]
        
        // 创建PageHeaderView实例进行测试
        let headerView = PageHeaderView()
        
        // 测试解析方法
        let parsedSignature = headerView.parseSignatureForTest(from: testData.personalitySign!, mapping: testData.mentionedUser)
        print("原始签名: \(testData.personalitySign!)")
        print("解析后签名: \(parsedSignature)")
        print("预期结果: 叫爸爸@嘿嘿一-^#")
        
        // 验证结果
        let expectedResult = "叫爸爸@嘿嘿一-^#"
        if parsedSignature == expectedResult {
            print("✅ 测试通过！解析结果正确")
        } else {
            print("❌ 测试失败！解析结果不正确")
        }
        
        // 测试富文本生成
        let attributedText = headerView.attributedSignatureForTest(from: parsedSignature, mentionedUser: testData.mentionedUser)
        print("富文本长度: \(attributedText.length)")
        print("富文本内容: \(attributedText.string)")

        // 测试链接属性
        attributedText.enumerateAttribute(.link, in: NSRange(location: 0, length: attributedText.length), options: []) { value, range, _ in
            if let linkValue = value as? String {
                let linkText = (attributedText.string as NSString).substring(with: range)
                print("发现链接: '\(linkText)' -> \(linkValue)")
            }
        }

        print("=== 测试完成 ===")
    }
}

// 为了测试，需要在PageHeaderView中添加公开的测试方法
extension PageHeaderView {
    
    /// 公开的测试方法 - 解析签名
    func parseSignatureForTest(from snapshot: String, mapping: [String: String]?) -> String {
        return parseSignature(from: snapshot, mapping: mapping)
    }
    
    /// 公开的测试方法 - 生成富文本
    func attributedSignatureForTest(from signature: String, mentionedUser: [String: String]?) -> NSAttributedString {
        return attributedSignature(from: signature, mentionedUser: mentionedUser)
    }
}
